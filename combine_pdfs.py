#!/usr/bin/env python3
"""
Simple PDF Combiner

This script combines multiple PDF files into one.

Requirements:
- PyMuPDF (fitz)

Usage:
    python combine_pdfs.py output.pdf input1.pdf input2.pdf input3.pdf ...
"""

import sys
import os

try:
    import fitz  # PyMuPDF
except ImportError:
    print("Installing PyMuPDF (fitz) package...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
    import fitz

def combine_pdfs(output_file, input_files):
    """Combine multiple PDFs into one."""
    
    # Create a new PDF document
    output_doc = fitz.open()
    
    # Process each input file
    for input_file in input_files:
        if not os.path.isfile(input_file):
            print(f"Warning: Input file '{input_file}' not found, skipping")
            continue
        
        print(f"Processing: {input_file}")
        doc = fitz.open(input_file)
        
        # Add all pages from this document to the output
        output_doc.insert_pdf(doc)
        
        # Close the input document
        doc.close()
    
    # Get the total page count
    page_count = output_doc.page_count
    print(f"Total pages in combined PDF: {page_count}")
    
    # Save and close the output document
    output_doc.save(output_file)
    output_doc.close()
    
    print(f"Created combined PDF: {output_file}")
    return output_file

def main():
    if len(sys.argv) < 3:
        print("Usage: python combine_pdfs.py output.pdf input1.pdf input2.pdf ...")
        return 1
    
    output_file = sys.argv[1]
    input_files = sys.argv[2:]
    
    combine_pdfs(output_file, input_files)
    return 0

if __name__ == '__main__':
    sys.exit(main())
