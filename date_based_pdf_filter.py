#!/usr/bin/env python3
"""
Date-based PDF Filter

This script analyzes a PDF to find dates and creates a filtered version with:
1. First page
2. Last 2 pages
3. At least 1 page from each month found in the PDF
4. Additional pages to reach target count (200 pages)

Requirements:
- PyMuPDF (fitz)
- dateutil

Usage:
    python date_based_pdf_filter.py input.pdf output.pdf [target_pages]
"""

import sys
import os
import re
from datetime import datetime
from collections import defaultdict
import random

try:
    import fitz  # PyMuPDF
except ImportError:
    print("Installing PyMuPDF (fitz) package...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
    import fitz

try:
    from dateutil import parser as date_parser
except ImportError:
    print("Installing python-dateutil package...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-dateutil"])
    from dateutil import parser as date_parser

def extract_dates_from_text(text):
    """Extract dates from text using various patterns."""
    dates = []
    
    # Common date patterns
    date_patterns = [
        r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # MM/DD/YYYY or MM-DD-YYYY
        r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',    # YYYY/MM/DD or YYYY-MM-DD
        r'\b\d{1,2}\s+\w+\s+\d{4}\b',          # DD Month YYYY
        r'\b\w+\s+\d{1,2},?\s+\d{4}\b',        # Month DD, YYYY
        r'\b\d{4}\s+\w+\s+\d{1,2}\b',          # YYYY Month DD
    ]
    
    for pattern in date_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            try:
                # Try to parse the date
                parsed_date = date_parser.parse(match, fuzzy=True)
                dates.append(parsed_date)
            except:
                continue
    
    return dates

def analyze_pdf_dates(input_file, sample_pages=50):
    """Analyze a PDF to find dates and group pages by month."""
    print(f"Analyzing dates in PDF: {input_file}")
    
    doc = fitz.open(input_file)
    total_pages = doc.page_count
    print(f"Total pages: {total_pages}")
    
    # Sample pages to analyze (analyzing all pages would be too slow)
    if total_pages <= sample_pages:
        pages_to_analyze = list(range(total_pages))
    else:
        # Sample pages evenly distributed throughout the document
        step = total_pages // sample_pages
        pages_to_analyze = list(range(0, total_pages, step))
    
    print(f"Analyzing {len(pages_to_analyze)} sample pages for dates...")
    
    page_dates = {}  # page_num -> list of dates found
    month_pages = defaultdict(list)  # (year, month) -> list of page numbers
    
    for page_num in pages_to_analyze:
        try:
            page = doc[page_num]
            text = page.get_text()
            
            # Extract dates from this page
            dates = extract_dates_from_text(text)
            
            if dates:
                page_dates[page_num] = dates
                # Group by month
                for date in dates:
                    month_key = (date.year, date.month)
                    month_pages[month_key].append(page_num)
        except Exception as e:
            print(f"Error processing page {page_num}: {e}")
            continue
    
    doc.close()
    
    print(f"Found dates on {len(page_dates)} pages")
    print(f"Found {len(month_pages)} unique months")
    
    # Print month summary
    for (year, month), pages in sorted(month_pages.items()):
        month_name = datetime(year, month, 1).strftime("%B %Y")
        print(f"  {month_name}: {len(pages)} pages with dates")
    
    return page_dates, month_pages, total_pages

def create_filtered_pdf(input_file, output_file, target_pages=200):
    """Create a filtered PDF with date-based selection."""
    
    # Analyze the PDF for dates
    page_dates, month_pages, total_pages = analyze_pdf_dates(input_file)
    
    # Start with required pages
    selected_pages = set()
    
    # Add first page
    selected_pages.add(0)
    print("Added first page (page 1)")
    
    # Add last 2 pages
    if total_pages > 1:
        selected_pages.add(total_pages - 1)
        print(f"Added last page (page {total_pages})")
    if total_pages > 2:
        selected_pages.add(total_pages - 2)
        print(f"Added second-to-last page (page {total_pages - 1})")
    
    # Add at least one page from each month
    for (year, month), pages in month_pages.items():
        if pages:
            # Choose the first page from this month that has dates
            page_to_add = min(pages)
            selected_pages.add(page_to_add)
            month_name = datetime(year, month, 1).strftime("%B %Y")
            print(f"Added page {page_to_add + 1} for {month_name}")
    
    print(f"Selected {len(selected_pages)} pages so far")
    
    # If we need more pages to reach the target
    remaining_pages_needed = target_pages - len(selected_pages)
    
    if remaining_pages_needed > 0:
        print(f"Need {remaining_pages_needed} more pages to reach {target_pages}")
        
        # Add random pages that aren't already selected
        available_pages = set(range(total_pages)) - selected_pages
        if len(available_pages) >= remaining_pages_needed:
            additional_pages = random.sample(list(available_pages), remaining_pages_needed)
            selected_pages.update(additional_pages)
            print(f"Added {len(additional_pages)} additional random pages")
        else:
            # Add all remaining pages if we don't have enough
            selected_pages.update(available_pages)
            print(f"Added all remaining {len(available_pages)} pages")
    
    # Convert to sorted list
    selected_pages = sorted(list(selected_pages))
    
    print(f"Final selection: {len(selected_pages)} pages")
    
    # Create the filtered PDF
    doc = fitz.open(input_file)
    output_doc = fitz.open()
    
    for page_num in selected_pages:
        output_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
    
    output_doc.save(output_file)
    output_doc.close()
    doc.close()
    
    print(f"Created filtered PDF: {output_file}")
    return len(selected_pages)

def main():
    if len(sys.argv) < 3:
        print("Usage: python date_based_pdf_filter.py input.pdf output.pdf [target_pages]")
        return 1
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    target_pages = int(sys.argv[3]) if len(sys.argv) > 3 else 200
    
    if not os.path.isfile(input_file):
        print(f"Error: Input file '{input_file}' not found")
        return 1
    
    final_pages = create_filtered_pdf(input_file, output_file, target_pages)
    print(f"Successfully created {output_file} with {final_pages} pages")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
